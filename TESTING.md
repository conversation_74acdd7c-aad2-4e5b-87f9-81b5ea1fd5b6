# B站评论提取功能测试指南

## 功能概述
在现有的B站字幕提取功能基础上，新增了评论提取功能。用户可以在B站视频详情页面通过点击"提取评论"按钮来获取当前视频的评论数据。

## 测试步骤

### 1. 环境准备
1. 确保Chrome浏览器已安装该扩展
2. 打开B站视频详情页面（如：https://www.bilibili.com/video/BV1xx411c7mD/）
3. 确保已设置有效的B站Cookie（通过扩展的侧边栏设置）

### 2. 基本功能测试

#### 2.1 按钮显示测试
- **测试内容**: 检查"提取评论"按钮是否正确显示
- **预期结果**: 
  - 在视频详情页面右下角应显示两个按钮
  - "提取字幕"按钮（蓝绿渐变）
  - "提取评论"按钮（橙色渐变）
- **测试方法**: 访问任意B站视频页面，观察右下角按钮

#### 2.2 评论获取测试
- **测试内容**: 点击"提取评论"按钮获取评论
- **预期结果**: 
  - 按钮文字变为"提取中..."
  - 显示进度条
  - 成功后弹出导出格式选择对话框
- **测试方法**: 
  1. 点击"提取评论"按钮
  2. 观察进度条和状态变化
  3. 等待处理完成

#### 2.3 导出功能测试
- **测试内容**: 测试不同格式的导出功能
- **预期结果**: 
  - JSON格式：下载包含完整结构化数据的JSON文件
  - TXT格式：下载纯文本格式的评论文件
  - 复制功能：评论内容复制到剪贴板
- **测试方法**: 
  1. 在导出对话框中分别点击三个按钮
  2. 验证文件下载和剪贴板内容

### 3. 数据格式验证

#### 3.1 JSON格式验证
检查JSON文件是否包含以下字段：
```json
{
  "title": "视频标题",
  "url": "视频URL",
  "comments": [
    {
      "id": "评论ID",
      "user": {
        "uid": "用户ID",
        "name": "用户名",
        "avatar": "头像URL",
        "level": "用户等级",
        "vip": "VIP状态"
      },
      "content": "评论内容",
      "like": "点赞数",
      "reply_count": "回复数",
      "time": "发布时间",
      "timestamp": "时间戳",
      "floor": "楼层",
      "replies": [
        {
          "id": "回复ID",
          "user": {...},
          "content": "回复内容",
          "like": "点赞数",
          "time": "回复时间",
          "timestamp": "时间戳",
          "parent_user": "被回复用户"
        }
      ]
    }
  ],
  "exportTime": "导出时间"
}
```

#### 3.2 TXT格式验证
检查TXT文件格式是否如下：
```
1. 用户名 (发布时间)
   评论内容
   👍 点赞数 | 💬 回复数
   └─ 回复用户: 回复内容
      (回复 @被回复用户)

2. 用户名 (发布时间)
   评论内容
   👍 点赞数 | 💬 回复数

...
```

### 4. 错误处理测试

#### 4.1 无评论视频测试
- **测试内容**: 测试没有评论的视频
- **预期结果**: 显示"该视频没有可用评论"提示
- **测试方法**: 找一个没有评论的视频进行测试

#### 4.2 网络错误测试
- **测试内容**: 测试网络异常情况
- **预期结果**: 显示相应的错误提示信息
- **测试方法**: 断网或使用无效Cookie进行测试

#### 4.3 非视频页面测试
- **测试内容**: 在非视频详情页点击按钮
- **预期结果**: 显示"评论提取功能仅支持B站视频详情页"提示
- **测试方法**: 在B站首页或其他页面测试

### 5. 性能测试

#### 5.1 大量评论测试
- **测试内容**: 测试评论数量较多的视频
- **预期结果**: 能够正常处理和导出
- **测试方法**: 选择热门视频进行测试

#### 5.2 响应时间测试
- **测试内容**: 记录从点击到完成的时间
- **预期结果**: 在合理时间内完成（通常10-30秒）
- **测试方法**: 多次测试并记录时间

### 6. 兼容性测试

#### 6.1 不同视频类型测试
- 普通视频
- 直播回放
- 番剧视频
- 课程视频

#### 6.2 不同浏览器测试
- Chrome（主要支持）
- Edge
- Firefox（如果支持）

## 已知限制

1. **分页限制**: 当前版本只获取第一页评论（通常20条）
2. **Cookie依赖**: 需要有效的B站Cookie才能获取评论
3. **API限制**: 受B站API调用频率限制
4. **页面限制**: 仅在视频详情页面可用

## 故障排除

### 常见问题及解决方案

1. **按钮不显示**
   - 检查是否在正确的页面（B站视频详情页）
   - 刷新页面重试

2. **获取评论失败**
   - 检查Cookie是否有效
   - 检查网络连接
   - 尝试重新获取Cookie

3. **导出失败**
   - 检查浏览器下载权限
   - 尝试不同的导出格式

4. **数据不完整**
   - 可能是API返回数据限制
   - 检查视频是否有评论权限限制

## 反馈渠道

如发现问题或有改进建议，请通过以下方式反馈：
- 扩展内的"反馈问题"按钮
- GitHub Issues
- 开发者邮箱

## 更新日志

### v1.1.0 (当前版本)
- ✅ 新增评论提取功能
- ✅ 支持JSON和TXT格式导出
- ✅ 支持复制到剪贴板
- ✅ 添加回复评论处理
- ✅ 完善错误处理机制
