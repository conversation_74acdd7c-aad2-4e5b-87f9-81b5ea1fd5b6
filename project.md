# B站字幕提取工具项目分析

## 项目概述
这是一个Chrome浏览器扩展，用于批量获取和导出B站视频字幕。主要功能包括：
- 批量获取B站视频字幕
- 支持多种导入方式（单个视频URL、当前页面地址、整个收藏夹导入）
- 支持SRT和TXT格式导出
- 表格化管理视频列表

## 核心文件结构
```
├── manifest.json          # 扩展配置文件
├── background.js          # 后台脚本，处理API请求和数据获取
├── content.js            # 内容脚本，在B站页面注入功能
├── sidepanel.html        # 侧边栏界面
├── script.js             # 前端逻辑脚本
├── data.js               # 数据处理相关
├── jszip.min.js          # ZIP文件处理库
└── README.md             # 项目说明文档
```

## 核心数据流程

### 1. 字幕获取流程
1. 用户在sidepanel中输入视频URL或通过按钮获取当前页面URL
2. script.js调用`fetchBilibiliVideoInfo()`获取视频基本信息
3. 通过`chrome.runtime.sendMessage`向background.js发送请求
4. background.js调用B站API获取视频信息（包括cid、aid、bvid等）
5. 使用获取的参数调用字幕API获取字幕数据
6. 返回格式化的字幕文本给前端

### 2. API接口使用
- 视频信息API: `https://api.bilibili.com/x/web-interface/view?bvid={bvid}`
- 字幕信息API: `https://api.bilibili.com/x/player/wbi/v2?aid={aid}&cid={cid}`
- 收藏夹API: `https://api.bilibili.com/x/v3/fav/resource/list?media_id={media_id}`

### 3. 权限配置
- `host_permissions`: 包含B站相关域名的访问权限
- `permissions`: 包括sidePanel、activeTab、tabs、downloads、storage等

## 关键技术点

### 1. 跨域请求处理
- 使用Chrome扩展的background script绕过CORS限制
- 通过`chrome.runtime.sendMessage`在content script和background script间通信

### 2. Cookie管理
- 支持用户手动设置Cookie以访问需要登录的内容
- 提供自动获取Cookie功能

### 3. 批量处理
- 支持表格化管理多个视频
- 提供进度条显示批量处理进度
- 支持导出为ZIP格式的批量字幕文件

## 现有按钮功能
1. **获取视频地址**: 获取当前B站视频页面的URL
2. **获取收藏夹地址**: 获取当前B站收藏夹内所有视频
3. **获取字幕**: 批量获取表格中所有视频的字幕
4. **导出字幕**: 导出已获取的字幕为SRT或TXT格式
5. **清空表格**: 清空当前表格中的所有视频数据
6. **提取评论**: 在视频详情页提取当前视频的评论数据（新增功能）

## 评论提取功能实现详情

### 已实现功能
✅ 在视频详情页添加"提取评论"按钮（橙色渐变样式）
✅ 调用B站评论API获取评论数据
✅ 格式化处理评论数据（用户信息、内容、点赞数、时间等）
✅ 提供评论数据导出功能（JSON、TXT格式）
✅ 支持复制评论到剪贴板
✅ 添加错误处理和重试机制
✅ 包含回复评论的处理

### 技术实现要点
1. **API接口**: `https://api.bilibili.com/x/v2/reply/wbi/main`
2. **关键参数**: oid（视频ID）、type=1、mode=3、plat=1、web_location=1315875
3. **认证**: 使用完整的Cookie认证信息
4. **跨域处理**: 通过background script处理API请求
5. **数据格式化**: 提取用户名、评论内容、点赞数、回复时间、楼层等信息
6. **回复处理**: 支持获取和格式化评论的回复内容

### 数据结构
- **用户信息**: uid、用户名、头像、等级、VIP状态
- **评论内容**: 评论ID、内容、点赞数、回复数、时间戳、楼层
- **回复信息**: 回复内容、回复用户、被回复用户
- **分页信息**: 是否有更多、下一页参数、总数

### 导出格式
1. **JSON格式**: 包含完整的结构化数据，适合程序处理
2. **TXT格式**: 纯文本格式，便于阅读和分析
3. **复制功能**: 直接复制到剪贴板，方便快速使用

### 常见问题及解决方案

#### 1. 访问权限不足错误
**问题**: 获取评论时提示"访问权限不足"
**原因**: Cookie未设置或无效，缺少必要的认证信息
**解决方案**:
1. 确保已登录B站账号
2. 使用扩展的"获取Cookie"功能自动获取
3. 手动设置Cookie（参考COOKIE_SETUP_GUIDE.md）
4. 验证Cookie包含关键字段：SESSDATA、bili_jct、DedeUserID

#### 2. 评论区相关错误
- **评论区已关闭**: 视频作者关闭评论功能
- **评论区仅对粉丝开放**: 需要关注UP主
- **视频不存在**: 检查视频链接有效性

#### 3. Cookie诊断功能
已添加详细的Cookie检查机制：
- 自动检测关键Cookie字段是否存在
- 在控制台输出详细的诊断信息
- 提供具体的错误提示和解决建议

### 待优化功能
🔄 支持分页加载更多评论
🔄 添加评论筛选功能（按点赞数、时间等）
🔄 支持批量获取多个视频的评论
🔄 添加评论数据统计分析功能
🔄 优化Cookie自动获取机制
