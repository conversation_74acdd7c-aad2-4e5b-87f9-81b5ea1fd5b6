// content.js

// 页面加载时，主动向background发送一条hello消息
// chrome.runtime.sendMessage({ from: 'content', type: 'hello', text: 'content script loaded' }, function(response) {
//   console.log('content.js 收到 background 回复:', response);
// });

// // 监听来自background的消息
// chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
//   console.log('content.js 收到 background 消息:', message);
//   // 可以根据message.type做不同处理
//   if (message.type === 'test') {
//     sendResponse({ from: 'content', text: '收到test消息' });
//   }
//   // 其他类型可扩展
// });
function extractSearchVideos() {
  const videos = []
  const seenUrls = new Set() // 用于去重

  // 辅助函数：解析播放量文本（例如 "64", "8902", "16.4万"）
  function parseViewsCount(viewText) {
    if (!viewText || viewText.toLowerCase() === "n/a" || viewText === "-")
      return "N/A"
    let num = parseFloat(String(viewText).replace(/,/g, "")) // 移除数字中的逗号
    if (String(viewText).includes("万")) {
      num *= 10000
    } else if (String(viewText).includes("k")) {
      // 有些国际化站点可能用k
      num *= 1000
    }
    return Math.round(num).toLocaleString() // 格式化数字，例如 12345 -> "12,345"
  }

  // 选择所有视频卡片元素
  // 根据你的HTML，视频卡片在 .video-list > div[class*="col_"] > .bili-video-card 结构下
  const videoCardElements = document.querySelectorAll(
    "div.video-list div.bili-video-card"
  )

  videoCardElements.forEach((card) => {
    const videoData = {}

    // 1. 提取 URL (优先从标题链接，其次从图片链接)
    let linkElement = card.querySelector(
      '.bili-video-card__info--right > a[href*="/video/BV"]'
    )
    if (!linkElement) {
      linkElement = card.querySelector(
        '.bili-video-card__wrap > a[href*="/video/BV"]'
      )
    }
    videoData.url = linkElement ? linkElement.href : "N/A"

    // 如果没有URL或者URL已经存在，则跳过（避免重复）
    if (videoData.url === "N/A" || seenUrls.has(videoData.url)) {
      return
    }
    seenUrls.add(videoData.url)

    // 2. 提取标题
    const titleElement = card.querySelector("h3.bili-video-card__info--tit")
    videoData.title = titleElement
      ? titleElement.getAttribute("title") ||
        titleElement.textContent.trim().replace(/\s+/g, " ")
      : "N/A"

    // 3. 提取时长
    const durationElement = card.querySelector(
      "span.bili-video-card__stats__duration"
    )
    videoData.duration = durationElement
      ? durationElement.textContent.trim()
      : "N/A"

    // 4. 提取播放量
    // 播放量通常是第一个 .bili-video-card__stats--item 里的最后一个span
    const viewStatElement = card.querySelector(
      '.bili-video-card__stats--left .bili-video-card__stats--item:nth-child(1) > span:not([class*="icon"])'
    )
    videoData.views = viewStatElement
      ? parseViewsCount(viewStatElement.textContent.trim())
      : "N/A"
    // 如果第一个没取到，尝试第二个（B站有时会调整顺序）
    if (videoData.views === "N/A") {
      const altViewStatElement = card.querySelector(
        '.bili-video-card__stats--left .bili-video-card__stats--item:nth-child(2) > span:not([class*="icon"])'
      )
      if (
        altViewStatElement &&
        !/\d{1,2}:\d{2}/.test(altViewStatElement.textContent.trim())
      ) {
        // 确保不是时长
        videoData.views = parseViewsCount(altViewStatElement.textContent.trim())
      }
    }

    // 5. 提取UP主和发布日期
    const ownerElement = card.querySelector("a.bili-video-card__info--owner")
    if (ownerElement) {
      const authorElement = ownerElement.querySelector(
        "span.bili-video-card__info--author"
      )
      videoData.author = authorElement
        ? authorElement.textContent.trim()
        : "N/A"

      const dateElement = ownerElement.querySelector(
        "span.bili-video-card__info--date"
      )
      videoData.date = dateElement
        ? dateElement.textContent.trim().replace(/^·\s*/, "")
        : "N/A" // 移除 " · " 前缀
    } else {
      videoData.author = "N/A"
      videoData.date = "N/A"
    }

    videoData.source = "Bili Video Card" // 标记来源
    videos.push(videoData)
  })

  return videos
}
// 你可以在这里扩展更多与页面交互的逻辑
function extractVideos() {
  const videos = []
  const seenUrls = new Set() // To avoid duplicates from different sections

  function parseViews(viewText) {
    if (!viewText) return "N/A"
    let num = parseFloat(viewText.replace(/,/g, "")) // Remove commas for numbers like "1,351"
    if (viewText.includes("万")) {
      num *= 10000
    }
    return Math.round(num).toLocaleString() // Format with commas for readability
  }

  // --- 1. Extract Top (Pinned/Featured) Video ---
  const topVideoSection = document.querySelector(".top-video")
  if (topVideoSection) {
    const linkElement = topVideoSection.querySelector(".bili-cover-card")
    const titleElement = topVideoSection.querySelector(".top-video__title")
    const statsElements = topVideoSection.querySelectorAll(".top-video__stat")
    const durationElement = topVideoSection.querySelector(
      ".bili-cover-card__stats .bili-cover-card__stat span"
    ) // Usually the first/only one here

    if (linkElement && titleElement) {
      const url = linkElement.href
      if (url && !seenUrls.has(url)) {
        const title = titleElement.title || titleElement.textContent.trim()
        let views = "N/A",
          date = "N/A"

        statsElements.forEach((statEl) => {
          const icon = statEl.querySelector("i")
          const text = statEl.textContent.trim()
          if (icon && icon.classList.contains("sic-BDC-playdata_square_line")) {
            views = parseViews(text)
          } else if (!icon) {
            // Date doesn't have an icon in this section
            date = text
          }
        })
        const duration = durationElement
          ? durationElement.textContent.trim()
          : "N/A"

        videos.push({ title, url, views, date, duration, source: "Top Video" })
        seenUrls.add(url)
      }
    }
  }

  // --- 2. Extract Videos from the "视频" (Videos) Section ---
  const videoSectionItems = document.querySelectorAll(
    "section.home-section .video-section .items__item"
  )
  videoSectionItems.forEach((item) => {
    const linkElement = item.querySelector(".bili-cover-card")
    const titleElement = item.querySelector(".bili-video-card__title a")
    const statsElements = item.querySelectorAll(".bili-cover-card__stat") // play, danmu, duration
    const dateElement = item.querySelector(".bili-video-card__subtitle span")

    if (linkElement && titleElement) {
      const url = linkElement.href
      if (url && !seenUrls.has(url)) {
        const title = titleElement.title || titleElement.textContent.trim()
        let views = "N/A",
          duration = "N/A"

        if (statsElements.length > 0) {
          // Views are typically the first stat
          const viewStatText = statsElements[0].textContent.trim()
          if (statsElements[0].querySelector(".sic-BDC-playdata_square_line")) {
            views = parseViews(viewStatText)
          }

          // Duration is typically the last stat
          if (statsElements.length > 1) {
            // Ensure there's more than one stat element
            const durationStatText =
              statsElements[statsElements.length - 1].textContent.trim()
            // Check if it looks like a duration (e.g., XX:XX)
            if (/\d{2}:\d{2}/.test(durationStatText)) {
              duration = durationStatText
            } else if (
              statsElements.length > 2 &&
              /\d{2}:\d{2}/.test(statsElements[1].textContent.trim())
            ) {
              // Sometimes duration is the middle element if views and danmu are present
              duration = statsElements[1].textContent.trim()
            }
          }
        }

        const date = dateElement ? dateElement.textContent.trim() : "N/A"
        videos.push({
          title,
          url,
          views,
          date,
          duration,
          source: "Video Section",
        })
        seenUrls.add(url)
      }
    }
  })

  // --- 3. Extract Videos from "合集" (Collections) Section ---
  // The structure is very similar to the "视频" section
  const collectionSectionItems = document.querySelectorAll(
    "section.home-section .lists-section .video-list__item"
  )
  collectionSectionItems.forEach((item) => {
    const linkElement = item.querySelector(".bili-cover-card")
    const titleElement = item.querySelector(".bili-video-card__title a")
    const statsElements = item.querySelectorAll(
      ".bili-cover-card__stats .bili-cover-card__stat"
    ) // play, duration
    const dateElement = item.querySelector(".bili-video-card__subtitle span")

    if (linkElement && titleElement) {
      const url = linkElement.href
      if (url && !seenUrls.has(url)) {
        const title = titleElement.title || titleElement.textContent.trim()
        let views = "N/A",
          duration = "N/A"

        statsElements.forEach((statEl) => {
          const text = statEl.textContent.trim()
          if (statEl.querySelector(".sic-BDC-playdata_square_line")) {
            views = parseViews(text)
          } else if (/\d{1,2}:\d{2}/.test(text)) {
            // Match MM:SS or M:SS
            duration = text
          }
        })

        const date = dateElement ? dateElement.textContent.trim() : "N/A"
        videos.push({
          title,
          url,
          views,
          date,
          duration,
          source: "Collection Section",
        })
        seenUrls.add(url)
      }
    }
  })

  return videos
}

// ====== 页面右下角插入按钮 ======
function createFloatingButton() {
  if (document.getElementById("extract-videos-btn")) return // 防止重复插入

  // 创建按钮容器
  const buttonContainer = document.createElement("div")
  buttonContainer.id = "floating-buttons-container"
  buttonContainer.style.position = "fixed"
  buttonContainer.style.right = "32px"
  buttonContainer.style.bottom = "32px"
  buttonContainer.style.zIndex = "99999"
  buttonContainer.style.display = "flex"
  buttonContainer.style.flexDirection = "column"
  buttonContainer.style.gap = "12px"

  // 创建字幕提取按钮
  const subtitleBtn = document.createElement("button")
  subtitleBtn.id = "extract-videos-btn"
  subtitleBtn.textContent = "提取字幕"
  subtitleBtn.style.background = "linear-gradient(90deg,#3370ff,#52c41a)"
  subtitleBtn.style.color = "#fff"
  subtitleBtn.style.border = "none"
  subtitleBtn.style.borderRadius = "24px"
  subtitleBtn.style.padding = "12px 28px"
  subtitleBtn.style.fontSize = "16px"
  subtitleBtn.style.boxShadow = "0 4px 16px rgba(51,112,255,0.15)"
  subtitleBtn.style.cursor = "pointer"
  subtitleBtn.style.transition = "all 0.2s"
  subtitleBtn.style.opacity = "0.92"
  subtitleBtn.onmouseenter = () => (subtitleBtn.style.opacity = "1")
  subtitleBtn.onmouseleave = () => (subtitleBtn.style.opacity = "0.92")

  // 创建评论提取按钮
  const commentsBtn = document.createElement("button")
  commentsBtn.id = "extract-comments-btn"
  commentsBtn.textContent = "提取评论"
  commentsBtn.style.background = "linear-gradient(90deg,#ff6b35,#f7931e)"
  commentsBtn.style.color = "#fff"
  commentsBtn.style.border = "none"
  commentsBtn.style.borderRadius = "24px"
  commentsBtn.style.padding = "12px 28px"
  commentsBtn.style.fontSize = "16px"
  commentsBtn.style.boxShadow = "0 4px 16px rgba(255,107,53,0.15)"
  commentsBtn.style.cursor = "pointer"
  commentsBtn.style.transition = "all 0.2s"
  commentsBtn.style.opacity = "0.92"
  commentsBtn.onmouseenter = () => (commentsBtn.style.opacity = "1")
  commentsBtn.onmouseleave = () => (commentsBtn.style.opacity = "0.92")

  // 字幕提取按钮点击事件
  subtitleBtn.onclick = async function () {
    subtitleBtn.disabled = true
    subtitleBtn.textContent = "提取中..."
    try {
      let videos = []
      const url = window.location.href
      if (url.startsWith("https://search.bilibili.com/")) {
        videos = extractSearchVideos()
      } else if (url.startsWith("https://space.bilibili.com/")) {
        videos = extractVideos()
      } else if (url.startsWith("https://www.bilibili.com/video/")) {
        // 视频详情页，直接用当前url和标题
        const title = document.title.replace(/_哔哩哔哩.*$/, "").trim()
        videos = [{ title, url }]
      } else {
        alert("当前页面暂不支持一键提取视频")
        subtitleBtn.disabled = false
        subtitleBtn.textContent = "提取字幕"
        return
      }
      if (!videos || videos.length === 0) {
        alert("未提取到任何视频")
        subtitleBtn.disabled = false
        subtitleBtn.textContent = "提取字幕"
        return
      }
      // 对videos按url去重
      videos = Array.from(new Map(videos.map((v) => [v.url, v])).values())
      // 2. 批量获取字幕（带进度条）
      showContentProgressBar("正在批量获取字幕...", 0)
      let subtitleResults = []
      for (let i = 0; i < videos.length; i++) {
        const v = videos[i]
        // 解析bvid
        const bvidMatch = v.url.match(/\/video\/(BV\w+)/)
        if (!bvidMatch) continue
        const bvid = bvidMatch[1]
        // 获取视频信息
        const info = await chrome.runtime.sendMessage({
          action: "fetchBilibiliInfo",
          videoId: bvid,
        })
        if (!info || !info.success || !info.cid) continue
        // 获取字幕
        const sub = await chrome.runtime.sendMessage({
          action: "fetchBilibiliSubtitle",
          cid: info.cid,
          bvid: info.bvid,
          aid: info.aid,
        })
        console.log("获取字幕响应:", sub)
        if (sub && sub.success) {
          subtitleResults.push({
            title: v.title,
            url: v.url,
            subtitle: sub && sub.success ? sub.subtitleText : "",
          })
        }
        // 更新进度条
        showContentProgressBar(
          `正在处理第${i + 1}/${videos.length}个视频...`,
          Math.round(((i + 1) / videos.length) * 100)
        )
      }
      hideContentProgressBar()
      // 3. 弹出导出格式选择弹窗
      showContentExportDialog(subtitleResults)
    } catch (e) {
      alert("批量处理失败：" + e.message)
    } finally {
      subtitleBtn.disabled = false
      subtitleBtn.textContent = "提取字幕"
    }
  }

  // 评论提取按钮点击事件
  commentsBtn.onclick = async function () {
    commentsBtn.disabled = true
    commentsBtn.textContent = "提取中..."
    try {
      const url = window.location.href
      if (!url.startsWith("https://www.bilibili.com/video/")) {
        alert("评论提取功能仅支持B站视频详情页")
        return
      }

      // 获取视频ID
      const bvidMatch = url.match(/\/video\/(BV\w+)/)
      if (!bvidMatch) {
        alert("无法识别视频ID")
        return
      }

      const bvid = bvidMatch[1]
      const title = document.title.replace(/_哔哩哔哩.*$/, "").trim()

      // 显示进度条
      showContentProgressBar("正在获取视频信息...", 10)

      // 获取视频信息以获取aid
      const videoInfo = await chrome.runtime.sendMessage({
        action: "fetchBilibiliInfo",
        videoId: bvid,
      })

      if (!videoInfo || !videoInfo.success || !videoInfo.aid) {
        alert("获取视频信息失败，无法提取评论")
        return
      }

      showContentProgressBar("正在获取评论数据...", 30)

      // 获取评论数据
      const commentsResult = await chrome.runtime.sendMessage({
        action: "fetchBilibiliComments",
        oid: videoInfo.aid,
        type: 1,
        mode: 3,
      })

      hideContentProgressBar()

      if (commentsResult && commentsResult.success) {
        // 显示评论导出对话框
        showCommentsExportDialog([
          {
            title: title,
            url: url,
            comments: commentsResult.comments,
            commentsText: commentsResult.commentsText,
          },
        ])
      } else {
        alert("获取评论失败：" + (commentsResult?.message || "未知错误"))
      }
    } catch (e) {
      alert("评论提取失败：" + e.message)
    } finally {
      commentsBtn.disabled = false
      commentsBtn.textContent = "提取评论"
      hideContentProgressBar()
    }
  }

  // 将按钮添加到容器
  buttonContainer.appendChild(subtitleBtn)
  buttonContainer.appendChild(commentsBtn)
  document.body.appendChild(buttonContainer)
}

function safeCreateFloatingButton() {
  if (document.body) {
    createFloatingButton()
  } else {
    document.addEventListener("DOMContentLoaded", createFloatingButton)
  }
}
safeCreateFloatingButton()

// 显示复制提示
function showCopyTip(msg) {
  let tip = document.getElementById("extract-videos-tip")
  if (!tip) {
    tip = document.createElement("div")
    tip.id = "extract-videos-tip"
    tip.style.position = "fixed"
    tip.style.right = "32px"
    tip.style.bottom = "80px"
    tip.style.zIndex = "99999"
    tip.style.background = "rgba(51,112,255,0.95)"
    tip.style.color = "#fff"
    tip.style.borderRadius = "16px"
    tip.style.padding = "10px 22px"
    tip.style.fontSize = "15px"
    tip.style.boxShadow = "0 2px 8px rgba(51,112,255,0.12)"
    tip.style.opacity = "0.98"
    tip.style.transition = "all 0.3s"
    document.body.appendChild(tip)
  }
  tip.textContent = msg
  tip.style.display = "block"
  setTimeout(() => {
    tip.style.display = "none"
  }, 2200)
}

function showContentProgressBar(text, percent) {
  let bar = document.getElementById("batch-progress-bar")
  if (!bar) {
    bar = document.createElement("div")
    bar.id = "batch-progress-bar"
    bar.style.position = "fixed"
    bar.style.right = "32px"
    bar.style.bottom = "120px"
    bar.style.zIndex = "99999"
    bar.style.background = "#fff"
    bar.style.border = "1px solid #3370ff"
    bar.style.borderRadius = "8px"
    bar.style.padding = "12px 24px"
    bar.style.boxShadow = "0 4px 16px rgba(51,112,255,0.15)"
    bar.innerHTML = `<div id="batch-progress-text"></div>
      <div style="width:200px;height:8px;background:#eee;border-radius:4px;overflow:hidden;margin-top:8px;">
        <div id="batch-progress-inner" style="height:8px;background:#3370ff;width:0%;transition:width 0.3s;"></div>
      </div>`
    document.body.appendChild(bar)
  }
  document.getElementById("batch-progress-text").textContent = text
  document.getElementById("batch-progress-inner").style.width = percent + "%"
}

function hideContentProgressBar() {
  const bar = document.getElementById("batch-progress-bar")
  if (bar) bar.remove()
}
function downloadTxt(content, name) {
  const blob = new Blob([content], { type: "text/plain;charset=utf-8" })
  const a = document.createElement("a")
  a.href = URL.createObjectURL(blob)
  a.download = name
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(a.href)
}
function showContentExportDialog(subtitleResults) {
  // 新增：无字幕时直接提示
  if (
    !subtitleResults ||
    subtitleResults.length === 0 ||
    subtitleResults.every((r) => !r.subtitle || !r.subtitle.trim())
  ) {
    showCopyTip("该视频没有可用字幕")
    return
  }
  // 创建弹窗背景
  const dialogOverlay = document.createElement("div")
  dialogOverlay.style.position = "fixed"
  dialogOverlay.style.top = "0"
  dialogOverlay.style.left = "0"
  dialogOverlay.style.width = "100%"
  dialogOverlay.style.height = "100%"
  dialogOverlay.style.backgroundColor = "rgba(0, 0, 0, 0.5)"
  dialogOverlay.style.display = "flex"
  dialogOverlay.style.justifyContent = "center"
  dialogOverlay.style.alignItems = "center"
  dialogOverlay.style.zIndex = "2000"
  dialogOverlay.style.backdropFilter = "blur(3px)"

  // 创建弹窗内容
  const dialogContent = document.createElement("div")
  dialogContent.style.backgroundColor = "white"
  dialogContent.style.borderRadius = "12px"
  dialogContent.style.padding = "20px"
  dialogContent.style.width = "360px"
  dialogContent.style.maxWidth = "90%"
  dialogContent.style.boxShadow = "0 10px 25px rgba(0, 0, 0, 0.15)"
  dialogContent.style.textAlign = "center"

  // 标题
  const title = document.createElement("h3")
  title.textContent = "选择字幕导出格式"
  title.style.margin = "0 0 16px 0"
  title.style.color = "#333"
  title.style.fontSize = "18px"

  // 合并导出勾选框
  const mergeContainer = document.createElement("div")
  mergeContainer.style.margin = "0 0 12px 0"
  mergeContainer.style.textAlign = "left"
  mergeContainer.style.display = "flex"
  mergeContainer.style.alignItems = "center"
  mergeContainer.style.gap = "8px"
  const mergeCheckbox = document.createElement("input")
  mergeCheckbox.type = "checkbox"
  mergeCheckbox.id = "mergeExportCheckbox"
  mergeCheckbox.style.transform = "scale(1.2)"
  mergeCheckbox.checked = true
  const mergeLabel = document.createElement("label")
  mergeLabel.htmlFor = "mergeExportCheckbox"
  mergeLabel.textContent = "合并导出为单文件"
  mergeLabel.style.fontSize = "14px"
  mergeLabel.style.color = "#333"
  mergeContainer.appendChild(mergeCheckbox)
  mergeContainer.appendChild(mergeLabel)

  // 按钮容器
  const buttonContainer = document.createElement("div")
  buttonContainer.style.display = "flex"
  buttonContainer.style.justifyContent = "center"
  buttonContainer.style.gap = "16px"
  buttonContainer.style.marginTop = "8px"

  // TXT按钮
  const txtButton = document.createElement("button")
  txtButton.textContent = "导出 TXT"
  txtButton.style.padding = "10px 16px"
  txtButton.style.backgroundColor = "#3370ff"
  txtButton.style.color = "white"
  txtButton.style.border = "none"
  txtButton.style.borderRadius = "6px"
  txtButton.style.cursor = "pointer"
  txtButton.style.fontWeight = "500"
  txtButton.style.flex = "1"
  txtButton.style.maxWidth = "140px"
  txtButton.style.transition = "all 0.3s ease"

  // 新增：复制TXT按钮
  const copyButton = document.createElement("button")
  copyButton.textContent = "复制 TXT"
  copyButton.style.padding = "10px 16px"
  copyButton.style.backgroundColor = "#52c41a"
  copyButton.style.color = "white"
  copyButton.style.border = "none"
  copyButton.style.borderRadius = "6px"
  copyButton.style.cursor = "pointer"
  copyButton.style.fontWeight = "500"
  copyButton.style.flex = "1"
  copyButton.style.maxWidth = "140px"
  copyButton.style.transition = "all 0.3s ease"

  // 取消按钮
  const cancelButton = document.createElement("button")
  cancelButton.textContent = "取消"
  cancelButton.style.marginTop = "16px"
  cancelButton.style.padding = "8px 16px"
  cancelButton.style.backgroundColor = "transparent"
  cancelButton.style.color = "#666"
  cancelButton.style.border = "none"
  cancelButton.style.borderRadius = "6px"
  cancelButton.style.cursor = "pointer"
  cancelButton.style.fontSize = "13px"

  // 导出TXT事件
  txtButton.addEventListener("click", () => {
    document.body.removeChild(dialogOverlay)
    const merge = mergeCheckbox.checked
    if (merge) {
      // 合并导出：下载合并文件
      let merged = ""
      subtitleResults.forEach((r) => {
        merged += `<video>${r.title}:\n${r.subtitle}\n</video>\n`
      })
      chrome.runtime.sendMessage({
        action: "downloadTxt",
        content: merged,
        filename: "bilibili_merged.txt",
      })
    } else {
      // 分文件导出：每个字幕单独下载
      subtitleResults.forEach((r) => {
        chrome.runtime.sendMessage({
          action: "downloadTxt",
          content: r.subtitle,
          filename: `${r.title.replace(/[\\/:*?\"<>|]/g, "_")}.txt`,
        })
      })
    }
  })

  // 复制TXT事件
  copyButton.addEventListener("click", () => {
    document.body.removeChild(dialogOverlay)
    const merge = mergeCheckbox.checked
    let merged = ""
    if (merge) {
      subtitleResults.forEach((r) => {
        merged += `<video>${r.title}:\n${r.subtitle}\n</video>\n`
      })
    } else {
      // 分文件复制：合并为一个文本
      merged = subtitleResults
        .map((r) => `<video>${r.title}:\n${r.subtitle}\n</video>`)
        .join("\n")
    }
    navigator.clipboard.writeText(merged).then(
      () => {
        showCopyTip("已复制到剪贴板！")
      },
      () => {
        showCopyTip("复制失败，请手动全选复制")
      }
    )
  })

  cancelButton.addEventListener("click", () => {
    document.body.removeChild(dialogOverlay)
  })

  // 组装弹窗
  dialogContent.appendChild(title)
  dialogContent.appendChild(mergeContainer)
  dialogContent.appendChild(buttonContainer)
  buttonContainer.appendChild(txtButton)
  buttonContainer.appendChild(copyButton)
  dialogContent.appendChild(cancelButton)
  dialogOverlay.appendChild(dialogContent)

  // 显示弹窗
  document.body.appendChild(dialogOverlay)
}

// 显示评论导出对话框
function showCommentsExportDialog(commentsResults) {
  // 检查是否有评论数据
  if (
    !commentsResults ||
    commentsResults.length === 0 ||
    commentsResults.every((r) => !r.comments || r.comments.length === 0)
  ) {
    showCopyTip("该视频没有可用评论")
    return
  }

  // 创建弹窗背景
  const dialogOverlay = document.createElement("div")
  dialogOverlay.style.position = "fixed"
  dialogOverlay.style.top = "0"
  dialogOverlay.style.left = "0"
  dialogOverlay.style.width = "100%"
  dialogOverlay.style.height = "100%"
  dialogOverlay.style.backgroundColor = "rgba(0, 0, 0, 0.5)"
  dialogOverlay.style.display = "flex"
  dialogOverlay.style.justifyContent = "center"
  dialogOverlay.style.alignItems = "center"
  dialogOverlay.style.zIndex = "2000"
  dialogOverlay.style.backdropFilter = "blur(3px)"

  // 创建弹窗内容
  const dialogContent = document.createElement("div")
  dialogContent.style.backgroundColor = "white"
  dialogContent.style.borderRadius = "12px"
  dialogContent.style.padding = "20px"
  dialogContent.style.width = "360px"
  dialogContent.style.maxWidth = "90%"
  dialogContent.style.boxShadow = "0 10px 25px rgba(0, 0, 0, 0.15)"
  dialogContent.style.textAlign = "center"

  // 标题
  const title = document.createElement("h3")
  title.textContent = "选择评论导出格式"
  title.style.margin = "0 0 16px 0"
  title.style.color = "#333"
  title.style.fontSize = "18px"

  // 按钮容器
  const buttonContainer = document.createElement("div")
  buttonContainer.style.display = "flex"
  buttonContainer.style.justifyContent = "center"
  buttonContainer.style.gap = "16px"
  buttonContainer.style.marginTop = "8px"

  // JSON按钮
  const jsonButton = document.createElement("button")
  jsonButton.textContent = "导出 JSON"
  jsonButton.style.padding = "10px 16px"
  jsonButton.style.backgroundColor = "#ff6b35"
  jsonButton.style.color = "white"
  jsonButton.style.border = "none"
  jsonButton.style.borderRadius = "6px"
  jsonButton.style.cursor = "pointer"
  jsonButton.style.fontWeight = "500"
  jsonButton.style.flex = "1"
  jsonButton.style.maxWidth = "140px"
  jsonButton.style.transition = "all 0.3s ease"

  // TXT按钮
  const txtButton = document.createElement("button")
  txtButton.textContent = "导出 TXT"
  txtButton.style.padding = "10px 16px"
  txtButton.style.backgroundColor = "#3370ff"
  txtButton.style.color = "white"
  txtButton.style.border = "none"
  txtButton.style.borderRadius = "6px"
  txtButton.style.cursor = "pointer"
  txtButton.style.fontWeight = "500"
  txtButton.style.flex = "1"
  txtButton.style.maxWidth = "140px"
  txtButton.style.transition = "all 0.3s ease"

  // 复制按钮
  const copyButton = document.createElement("button")
  copyButton.textContent = "复制 TXT"
  copyButton.style.padding = "10px 16px"
  copyButton.style.backgroundColor = "#52c41a"
  copyButton.style.color = "white"
  copyButton.style.border = "none"
  copyButton.style.borderRadius = "6px"
  copyButton.style.cursor = "pointer"
  copyButton.style.fontWeight = "500"
  copyButton.style.flex = "1"
  copyButton.style.maxWidth = "140px"
  copyButton.style.transition = "all 0.3s ease"

  // 取消按钮
  const cancelButton = document.createElement("button")
  cancelButton.textContent = "取消"
  cancelButton.style.marginTop = "16px"
  cancelButton.style.padding = "8px 16px"
  cancelButton.style.backgroundColor = "transparent"
  cancelButton.style.color = "#666"
  cancelButton.style.border = "none"
  cancelButton.style.borderRadius = "6px"
  cancelButton.style.cursor = "pointer"
  cancelButton.style.fontSize = "13px"

  // 导出JSON事件
  jsonButton.addEventListener("click", () => {
    document.body.removeChild(dialogOverlay)
    const result = commentsResults[0]
    const jsonData = {
      title: result.title,
      url: result.url,
      comments: result.comments,
      exportTime: new Date().toISOString(),
    }
    chrome.runtime.sendMessage({
      action: "downloadTxt",
      content: JSON.stringify(jsonData, null, 2),
      filename: `${result.title.replace(/[\\/:*?\"<>|]/g, "_")}_comments.json`,
    })
  })

  // 导出TXT事件
  txtButton.addEventListener("click", () => {
    document.body.removeChild(dialogOverlay)
    const result = commentsResults[0]
    chrome.runtime.sendMessage({
      action: "downloadTxt",
      content: result.commentsText,
      filename: `${result.title.replace(/[\\/:*?\"<>|]/g, "_")}_comments.txt`,
    })
  })

  // 复制TXT事件
  copyButton.addEventListener("click", () => {
    document.body.removeChild(dialogOverlay)
    const result = commentsResults[0]
    navigator.clipboard.writeText(result.commentsText).then(
      () => {
        showCopyTip("评论已复制到剪贴板！")
      },
      () => {
        showCopyTip("复制失败，请手动全选复制")
      }
    )
  })

  cancelButton.addEventListener("click", () => {
    document.body.removeChild(dialogOverlay)
  })

  // 组装弹窗
  dialogContent.appendChild(title)
  dialogContent.appendChild(buttonContainer)
  buttonContainer.appendChild(jsonButton)
  buttonContainer.appendChild(txtButton)
  buttonContainer.appendChild(copyButton)
  dialogContent.appendChild(cancelButton)
  dialogOverlay.appendChild(dialogContent)

  // 显示弹窗
  document.body.appendChild(dialogOverlay)
}