# 📝 SubBatch - B 站字幕批量处理工具

## 🌟 插件介绍

SubBatch 是一个强大的浏览器扩展插件，专为批量获取和导出 B 站视频字幕而设计。它可以帮助用户轻松获取单个或批量视频的字幕数据，并支持导出为 SRT 或 TXT 格式的字幕文件，特别适合用于 AI 知识库建设（如 NotebookLM）、学习笔记整理或视频内容分析。

> 💡 特别适合将 B 站优质内容转化为个人 AI 知识库的素材，一键批量导出纯文本格式，无需手动复制粘贴！

## ✨ 主要功能

- **🎬 批量获取字幕**：同时处理多个 B 站视频的字幕数据
- **📥 多种导入方式**：支持单个视频 URL、当前页面地址、整个收藏夹导入
- **📤 灵活导出格式**：
  - **SRT 格式**：包含时间轴的标准字幕格式，适合播放器使用
  - **TXT 格式**：纯文本格式，完美适配 NotebookLM 等 AI 知识库
- **📊 视频列表管理**：表格化展示所有导入的视频，清晰直观
- **🔄 批量处理**：一键操作，节省时间
- **🔒 隐私保护**：本地处理数据，不上传到第三方服务器

## 📋 使用指南

### 🚀 快速开始

1. 安装扩展后，点击浏览器工具栏中的 SubBatch 图标
2. 在弹出的侧边栏中使用以下任一方法添加视频：
   - 输入 B 站视频 URL 并点击"添加到表格"
   - 在 B 站视频页面点击"获取视频地址"按钮
   - 在 B 站收藏夹页面点击"获取收藏夹"按钮

### 🧩 界面和按钮说明

#### 顶部按钮区

| 按钮                | 功能说明                            |
| ------------------- | ----------------------------------- |
| 📚 **使用文档**     | 打开本插件的详细使用文档和教程      |
| 💬 **反馈问题**     | 前往反馈页面提交使用中遇到的问题    |
| ❤️ **赞助我**       | 如果您觉得插件有用，可以支持开发者  |
| 🎬 **获取视频地址** | 获取当前浏览的 B 站视频页面 URL     |
| 📂 **获取收藏夹**   | 获取当前浏览的 B 站收藏夹内所有视频 |
| 🌟 **关注我**       | 前往开发者的 B 站主页关注           |

#### 输入区域

| 字段           | 说明                                                      |
| -------------- | --------------------------------------------------------- |
| **Cookie**     | 输入 B 站 Cookie 以获取需要登录才能观看的视频字幕         |
| **视频地址**   | 输入单个 B 站视频 URL，点击右侧"添加到表格"按钮添加       |
| **收藏夹地址** | 输入 B 站收藏夹 URL，点击右侧"获取收藏夹视频"按钮批量导入 |

#### 视频列表操作按钮

| 按钮            | 功能说明                                     |
| --------------- | -------------------------------------------- |
| 🎬 **获取字幕** | 批量获取表格中所有视频的字幕数据             |
| 📥 **导出字幕** | 将已获取字幕的视频批量导出为 SRT 或 TXT 格式 |
| 🗑️ **清空表格** | 清空当前表格中的所有视频数据                 |

#### 视频列表表格

表格显示所有已添加的视频，包含以下列：

- **序号**：视频在列表中的位置
- **视频标题**：点击可跳转到对应视频页面
- **作者**：视频的 UP 主
- **字幕状态**：显示字幕获取状态（未获取/获取中/已获取/获取失败）
- **操作**：可删除单个视频

## 🔍 详细使用流程

### 1️⃣ 获取视频字幕

1. 通过以上提到的任一方式将视频添加到表格
2. 点击"获取字幕"按钮开始批量获取字幕
3. 进度条会显示当前处理进度
4. 获取完成后，表格中的字幕状态会更新为"已获取"或错误信息

### 2️⃣ 导出字幕文件

1. 确保至少有一个视频的字幕状态为"已获取"
2. 点击"导出字幕"按钮
3. 在弹出的格式选择对话框中选择：
   - **SRT 格式**：包含时间轴和序号的标准字幕格式，适合导入播放器
   - **TXT 格式**：仅包含文本内容，适合导入 NotebookLM 等 AI 知识库
4. 选择后将自动下载为 ZIP 压缩包，包含所有字幕文件

### 3️⃣ 使用收藏夹批量导入

1. 打开 B 站收藏夹页面
2. 点击"获取收藏夹地址"按钮或手动复制 URL 到收藏夹地址输入框
3. 点击"获取收藏夹视频"按钮
4. 收藏夹中的所有视频将被添加到表格中

## 💡 使用技巧

- **选择合适的格式**：
  - 用于视频剪辑或研究时间轴 → 选择 SRT 格式
  - 用于 AI 知识库或文本分析 → 选择 TXT 格式
- **批量处理**：先将所有需要的视频添加到表格，然后一次性获取字幕，提高效率
- **字幕筛选**：如果某些视频获取失败，可先清除，再重试单个视频
- **Cookie 设置**：对于部分需要登录才能观看的视频，需设置 Cookie 才能获取字幕

## 🛠️ 常见问题

- **字幕获取失败**：检查视频是否有字幕，或尝试设置 Cookie
- **收藏夹导入失败**：确认收藏夹 URL 格式正确，可能需要设置 Cookie
- **下载的 ZIP 文件打不开**：使用最新版解压软件，或检查是否下载完整

## 📊 AI 知识库应用场景

SubBatch 特别适合用于构建个人知识库，尤其是与 NotebookLM 等 AI 工具配合使用：

1. 使用 SubBatch 批量导出感兴趣 B 站视频的字幕为 TXT 格式
2. 将 TXT 文件导入 NotebookLM 或其他 AI 笔记工具
3. 利用 AI 对内容进行总结、提取要点或生成学习笔记
4. 建立个人专题知识库，随时通过 AI 助手检索和学习

## 🙏 鸣谢

感谢使用 SubBatch 插件！如有任何使用问题或建议，请通过"反馈问题"按钮联系我们。

---

_SubBatch 仅供学习与交流使用，禁止用于任何商业用途。使用本工具获取的 B 站数据，用户应遵守国家相关法律法规及 B 站平台规范，严禁用于侵犯他人合法权益的行为。_
